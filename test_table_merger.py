#!/usr/bin/env python3
"""
表格合并器测试脚本
测试TableMerger类的三种合并场景
"""

import os
import sys
from pathlib import Path
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from table_merger import TableMerger


def read_html_file(file_path):
    """读取HTML文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"文件未找到: {file_path}")
        return None
    except Exception as e:
        print(f"读取文件失败 {file_path}: {e}")
        return None


def save_result(content, filename):
    """保存测试结果到文件"""
    output_dir = Path("test_results")
    output_dir.mkdir(exist_ok=True)
    
    output_path = output_dir / filename
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"结果已保存到: {output_path}")
    except Exception as e:
        print(f"保存文件失败: {e}")


def analyze_table_structure(html_content, title=""):
    """分析表格结构"""
    print(f"\n=== {title} ===")
    soup = BeautifulSoup(html_content, 'lxml')
    tables = soup.find_all('table')
    
    print(f"表格数量: {len(tables)}")
    
    for i, table in enumerate(tables, 1):
        print(f"\n表格 {i}:")
        
        # 检查caption
        caption = table.find('caption')
        if caption:
            print(f"  标题: {caption.get_text(strip=True)}")
        
        # 检查表头
        thead = table.find('thead')
        if thead:
            thead_rows = thead.find_all('tr')
            print(f"  表头行数: {len(thead_rows)}")
        
        # 检查表体
        tbody = table.find('tbody')
        if tbody:
            tbody_rows = tbody.find_all('tr')
            print(f"  数据行数: {len(tbody_rows)}")
        else:
            # 如果没有tbody，计算所有tr
            all_rows = table.find_all('tr')
            header_rows = len(thead.find_all('tr')) if thead else 0
            data_rows = len(all_rows) - header_rows
            print(f"  数据行数: {data_rows}")
        
        # 检查合并单元格
        cells_with_rowspan = table.find_all(['td', 'th'], rowspan=True)
        cells_with_colspan = table.find_all(['td', 'th'], colspan=True)
        
        if cells_with_rowspan:
            print(f"  包含rowspan的单元格: {len(cells_with_rowspan)}")
        if cells_with_colspan:
            print(f"  包含colspan的单元格: {len(cells_with_colspan)}")


def test_new_table():
    """测试场景1：新表格（不合并）"""
    print("\n" + "="*50)
    print("测试场景1：新表格（不合并）")
    print("="*50)
    
    merger = TableMerger()
    
    # 读取测试文件
    current_html = read_html_file("test_tables/test_page1.html")
    if not current_html:
        return
    
    # 处理新表格
    result_html, last_row = merger.process_page_result(
        current_page_html=current_html,
        judgment_result="新表格"
    )
    
    print(f"处理结果: 新表格")
    print(f"最后一行数据: {last_row}")
    
    # 分析结果
    analyze_table_structure(result_html, "新表格处理结果")
    
    # 保存结果
    save_result(result_html, "test_new_table_result.html")
    
    return result_html, last_row


def test_append_rows():
    """测试场景2：续表，开始新行"""
    print("\n" + "="*50)
    print("测试场景2：续表，开始新行")
    print("="*50)
    
    merger = TableMerger()
    
    # 读取测试文件
    previous_html = read_html_file("test_tables/test_page1.html")
    current_html = read_html_file("test_tables/test_page2_new_rows.html")
    
    if not previous_html or not current_html:
        return
    
    # 分析原始表格
    analyze_table_structure(previous_html, "第1页原始表格")
    analyze_table_structure(current_html, "第2页续表（新行）")
    
    # 处理续表
    result_html, last_row = merger.process_page_result(
        current_page_html=current_html,
        judgment_result="续表，开始新行",
        previous_page_html=previous_html
    )
    
    print(f"处理结果: 续表，开始新行")
    print(f"最后一行数据: {last_row}")
    
    # 分析合并结果
    analyze_table_structure(result_html, "续表合并结果（新行）")
    
    # 保存结果
    save_result(result_html, "test_append_rows_result.html")
    
    return result_html, last_row


def test_merge_first_row():
    """测试场景3：续表，合并首行"""
    print("\n" + "="*50)
    print("测试场景3：续表，合并首行")
    print("="*50)
    
    merger = TableMerger()
    
    # 读取测试文件
    previous_html = read_html_file("test_tables/test_page1.html")
    current_html = read_html_file("test_tables/test_page2_merge_first.html")
    
    if not previous_html or not current_html:
        return
    
    # 分析原始表格
    analyze_table_structure(previous_html, "第1页原始表格")
    analyze_table_structure(current_html, "第2页续表（合并首行）")
    
    # 处理续表
    result_html, last_row = merger.process_page_result(
        current_page_html=current_html,
        judgment_result="续表，合并首行",
        previous_page_html=previous_html
    )
    
    print(f"处理结果: 续表，合并首行")
    print(f"最后一行数据: {last_row}")
    
    # 分析合并结果
    analyze_table_structure(result_html, "续表合并结果（合并首行）")
    
    # 保存结果
    save_result(result_html, "test_merge_first_row_result.html")
    
    return result_html, last_row


def test_complex_table():
    """测试复杂表格处理"""
    print("\n" + "="*50)
    print("测试场景4：复杂表格分析")
    print("="*50)
    
    merger = TableMerger()
    
    # 读取复杂表格
    complex_html = read_html_file("test_tables/test_complex_table.html")
    if not complex_html:
        return
    
    # 分析复杂表格结构
    analyze_table_structure(complex_html, "复杂表格结构分析")
    
    # 测试提取最后一行
    last_row = merger.extract_table_last_row(complex_html)
    print(f"\n提取的最后一行数据: {last_row}")
    
    # 测试验证功能
    is_valid = merger.validate_merge_result(complex_html)
    print(f"表格结构验证结果: {'通过' if is_valid else '失败'}")
    
    return complex_html


def main():
    """主测试函数"""
    print("表格合并器测试开始")
    print("="*60)
    
    # 检查测试文件是否存在
    test_files = [
        "test_tables/test_page1.html",
        "test_tables/test_page2_new_rows.html", 
        "test_tables/test_page2_merge_first.html",
        "test_tables/test_complex_table.html"
    ]
    
    missing_files = []
    for file_path in test_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("缺少测试文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("请先创建测试文件后再运行测试。")
        return
    
    try:
        # 执行各种测试场景
        test_new_table()
        test_append_rows()
        test_merge_first_row()
        test_complex_table()
        
        print("\n" + "="*60)
        print("所有测试完成！")
        print("请查看 test_results/ 目录中的结果文件。")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
