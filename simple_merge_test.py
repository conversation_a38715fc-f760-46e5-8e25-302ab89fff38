#!/usr/bin/env python3
"""
简化的表格合并测试脚本
直接测试TableMerger的核心功能
"""

import sys
from pathlib import Path
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from table_merger import TableMerger


def create_test_html_1():
    """创建第一页测试HTML"""
    return """
    <table border="1">
        <caption>财务数据表</caption>
        <thead>
            <tr>
                <th>项目</th>
                <th>本期</th>
                <th>上期</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>营业收入</td>
                <td>1000万</td>
                <td>900万</td>
            </tr>
            <tr>
                <td>净利润</td>
                <td>100万</td>
                <td>80万</td>
            </tr>
            <tr>
                <td>总资产</td>
                <td>5000</td>
                <td>4500</td>
            </tr>
        </tbody>
    </table>
    """


def create_test_html_2_new_rows():
    """创建第二页测试HTML（新行）"""
    return """
    <table border="1">
        <tbody>
            <tr>
                <td>股东权益</td>
                <td>3000万</td>
                <td>2800万</td>
            </tr>
            <tr>
                <td>负债总额</td>
                <td>2000万</td>
                <td>1700万</td>
            </tr>
        </tbody>
    </table>
    """


def create_test_html_2_merge_first():
    """创建第二页测试HTML（合并首行）"""
    return """
    <table border="1">
        <tbody>
            <tr>
                <td>万</td>
                <td>万</td>
                <td></td>
            </tr>
            <tr>
                <td>股东权益</td>
                <td>3000万</td>
                <td>2800万</td>
            </tr>
        </tbody>
    </table>
    """


def print_table_content(html, title):
    """打印表格内容"""
    print(f"\n=== {title} ===")
    soup = BeautifulSoup(html, 'lxml')
    tables = soup.find_all('table')
    
    for i, table in enumerate(tables, 1):
        print(f"表格 {i}:")
        
        # 打印caption
        caption = table.find('caption')
        if caption:
            print(f"  标题: {caption.get_text(strip=True)}")
        
        # 打印所有行
        rows = table.find_all('tr')
        for j, row in enumerate(rows, 1):
            cells = row.find_all(['td', 'th'])
            cell_texts = [cell.get_text(strip=True) for cell in cells]
            print(f"  行{j}: {' | '.join(cell_texts)}")


def test_scenario_1():
    """测试场景1：新表格"""
    print("\n" + "="*60)
    print("测试场景1：新表格（不合并）")
    print("="*60)
    
    merger = TableMerger()
    html = create_test_html_1()
    
    result_html, last_row = merger.process_page_result(
        current_page_html=html,
        judgment_result="新表格"
    )
    
    print_table_content(html, "原始表格")
    print_table_content(result_html, "处理结果")
    print(f"最后一行数据: {last_row}")


def test_scenario_2():
    """测试场景2：续表，开始新行"""
    print("\n" + "="*60)
    print("测试场景2：续表，开始新行")
    print("="*60)
    
    merger = TableMerger()
    html1 = create_test_html_1()
    html2 = create_test_html_2_new_rows()
    
    print_table_content(html1, "第1页表格")
    print_table_content(html2, "第2页表格（新行）")
    
    result_html, last_row = merger.process_page_result(
        current_page_html=html2,
        judgment_result="续表，开始新行",
        previous_page_html=html1
    )
    
    print_table_content(result_html, "合并结果")
    print(f"最后一行数据: {last_row}")


def test_scenario_3():
    """测试场景3：续表，合并首行"""
    print("\n" + "="*60)
    print("测试场景3：续表，合并首行")
    print("="*60)
    
    merger = TableMerger()
    html1 = create_test_html_1()
    html2 = create_test_html_2_merge_first()
    
    print_table_content(html1, "第1页表格")
    print_table_content(html2, "第2页表格（合并首行）")
    
    result_html, last_row = merger.process_page_result(
        current_page_html=html2,
        judgment_result="续表，合并首行",
        previous_page_html=html1
    )
    
    print_table_content(result_html, "合并结果")
    print(f"最后一行数据: {last_row}")


def test_complex_merge():
    """测试复杂合并场景"""
    print("\n" + "="*60)
    print("测试场景4：复杂表格合并")
    print("="*60)
    
    # 创建包含rowspan和colspan的复杂表格
    complex_html1 = """
    <table border="1">
        <thead>
            <tr>
                <th rowspan="2">项目</th>
                <th colspan="2">2024年</th>
            </tr>
            <tr>
                <th>Q1</th>
                <th>Q2</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>营业收入</td>
                <td>1000</td>
                <td>1100</td>
            </tr>
            <tr>
                <td>净利润</td>
                <td>100</td>
                <td>120</td>
            </tr>
        </tbody>
    </table>
    """
    
    complex_html2 = """
    <table border="1">
        <tbody>
            <tr>
                <td>毛利率</td>
                <td>25%</td>
                <td>28%</td>
            </tr>
            <tr>
                <td>净利率</td>
                <td>10%</td>
                <td>11%</td>
            </tr>
        </tbody>
    </table>
    """
    
    merger = TableMerger()
    
    print_table_content(complex_html1, "复杂表格第1页")
    print_table_content(complex_html2, "复杂表格第2页")
    
    result_html, last_row = merger.process_page_result(
        current_page_html=complex_html2,
        judgment_result="续表，开始新行",
        previous_page_html=complex_html1
    )
    
    print_table_content(result_html, "复杂表格合并结果")
    print(f"最后一行数据: {last_row}")


def main():
    """主测试函数"""
    print("表格合并器核心功能测试")
    print("="*60)
    
    try:
        test_scenario_1()
        test_scenario_2()
        test_scenario_3()
        test_complex_merge()
        
        print("\n" + "="*60)
        print("所有测试完成！")
        print("="*60)
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
