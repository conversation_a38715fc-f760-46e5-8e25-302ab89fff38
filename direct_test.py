#!/usr/bin/env python3
"""
直接测试表格合并功能
"""

from bs4 import BeautifulSoup

def test_basic_merge():
    """测试基本的表格合并功能"""
    print("=== 基本表格合并测试 ===")
    
    # 第一页表格
    html1 = """
    <table border="1">
        <caption>财务数据表</caption>
        <thead>
            <tr>
                <th>项目</th>
                <th>本期</th>
                <th>上期</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>营业收入</td>
                <td>1000万</td>
                <td>900万</td>
            </tr>
            <tr>
                <td>净利润</td>
                <td>100万</td>
                <td>80万</td>
            </tr>
        </tbody>
    </table>
    """
    
    # 第二页表格（续表）
    html2 = """
    <table border="1">
        <tbody>
            <tr>
                <td>总资产</td>
                <td>5000万</td>
                <td>4500万</td>
            </tr>
            <tr>
                <td>股东权益</td>
                <td>3000万</td>
                <td>2800万</td>
            </tr>
        </tbody>
    </table>
    """
    
    print("第一页表格:")
    print_table_content(html1)
    
    print("\n第二页表格:")
    print_table_content(html2)
    
    # 手动合并表格
    merged_html = manual_merge_tables(html1, html2)
    
    print("\n合并后的表格:")
    print_table_content(merged_html)


def test_merge_first_row():
    """测试合并首行的功能"""
    print("\n=== 合并首行测试 ===")
    
    # 第一页表格（最后一行不完整）
    html1 = """
    <table border="1">
        <tbody>
            <tr>
                <td>营业收入</td>
                <td>1000万</td>
                <td>900万</td>
            </tr>
            <tr>
                <td>净利润</td>
                <td>100</td>
                <td>80</td>
            </tr>
        </tbody>
    </table>
    """
    
    # 第二页表格（第一行是续行）
    html2 = """
    <table border="1">
        <tbody>
            <tr>
                <td>万</td>
                <td>万</td>
                <td></td>
            </tr>
            <tr>
                <td>总资产</td>
                <td>5000万</td>
                <td>4500万</td>
            </tr>
        </tbody>
    </table>
    """
    
    print("第一页表格:")
    print_table_content(html1)
    
    print("\n第二页表格:")
    print_table_content(html2)
    
    # 手动合并首行
    merged_html = manual_merge_first_row(html1, html2)
    
    print("\n合并后的表格:")
    print_table_content(merged_html)


def test_complex_table():
    """测试复杂表格（包含rowspan和colspan）"""
    print("\n=== 复杂表格测试 ===")
    
    html = """
    <table border="1">
        <caption>季度财务数据对比表</caption>
        <thead>
            <tr>
                <th rowspan="2">财务指标</th>
                <th colspan="2">2024年</th>
                <th colspan="2">2023年</th>
            </tr>
            <tr>
                <th>Q1</th>
                <th>Q2</th>
                <th>Q1</th>
                <th>Q2</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>营业收入（万元）</td>
                <td>1000</td>
                <td>1100</td>
                <td>900</td>
                <td>950</td>
            </tr>
            <tr>
                <td>净利润（万元）</td>
                <td>100</td>
                <td>120</td>
                <td>80</td>
                <td>90</td>
            </tr>
            <tr>
                <td>毛利率（%）</td>
                <td>25</td>
                <td>28</td>
                <td>22</td>
                <td>24</td>
            </tr>
        </tbody>
    </table>
    """
    
    print("复杂表格结构:")
    print_table_content(html)
    analyze_table_structure(html)


def print_table_content(html):
    """打印表格内容"""
    soup = BeautifulSoup(html, 'lxml')
    tables = soup.find_all('table')
    
    for i, table in enumerate(tables, 1):
        # 打印caption
        caption = table.find('caption')
        if caption:
            print(f"  标题: {caption.get_text(strip=True)}")
        
        # 打印所有行
        rows = table.find_all('tr')
        for j, row in enumerate(rows, 1):
            cells = row.find_all(['td', 'th'])
            cell_texts = []
            for cell in cells:
                text = cell.get_text(strip=True)
                # 显示合并单元格信息
                rowspan = cell.get('rowspan', '1')
                colspan = cell.get('colspan', '1')
                if rowspan != '1' or colspan != '1':
                    text += f"[{rowspan}x{colspan}]"
                cell_texts.append(text)
            print(f"  行{j}: {' | '.join(cell_texts)}")


def analyze_table_structure(html):
    """分析表格结构"""
    soup = BeautifulSoup(html, 'lxml')
    tables = soup.find_all('table')
    
    for i, table in enumerate(tables, 1):
        print(f"\n表格 {i} 结构分析:")
        
        # 统计表头和数据行
        thead = table.find('thead')
        tbody = table.find('tbody')
        
        if thead:
            thead_rows = thead.find_all('tr')
            print(f"  表头行数: {len(thead_rows)}")
        
        if tbody:
            tbody_rows = tbody.find_all('tr')
            print(f"  数据行数: {len(tbody_rows)}")
        
        # 统计合并单元格
        cells_with_rowspan = table.find_all(['td', 'th'], rowspan=True)
        cells_with_colspan = table.find_all(['td', 'th'], colspan=True)
        
        if cells_with_rowspan:
            print(f"  包含rowspan的单元格: {len(cells_with_rowspan)}")
            for cell in cells_with_rowspan:
                print(f"    - {cell.get_text(strip=True)}: rowspan={cell.get('rowspan')}")
        
        if cells_with_colspan:
            print(f"  包含colspan的单元格: {len(cells_with_colspan)}")
            for cell in cells_with_colspan:
                print(f"    - {cell.get_text(strip=True)}: colspan={cell.get('colspan')}")


def manual_merge_tables(html1, html2):
    """手动合并两个表格"""
    soup1 = BeautifulSoup(html1, 'lxml')
    soup2 = BeautifulSoup(html2, 'lxml')
    
    table1 = soup1.find('table')
    table2 = soup2.find('table')
    
    # 获取第一个表格的tbody
    tbody1 = table1.find('tbody')
    if not tbody1:
        tbody1 = soup1.new_tag('tbody')
        table1.append(tbody1)
    
    # 获取第二个表格的所有行
    rows2 = table2.find_all('tr')
    
    # 将第二个表格的行添加到第一个表格
    for row in rows2:
        new_row = soup1.new_tag('tr')
        for cell in row.find_all(['td', 'th']):
            new_cell = soup1.new_tag('td')
            new_cell.string = cell.get_text(strip=True)
            # 复制属性
            for attr, value in cell.attrs.items():
                if attr in ['rowspan', 'colspan', 'class', 'style']:
                    new_cell[attr] = value
            new_row.append(new_cell)
        tbody1.append(new_row)
    
    return str(soup1)


def manual_merge_first_row(html1, html2):
    """手动合并首行"""
    soup1 = BeautifulSoup(html1, 'lxml')
    soup2 = BeautifulSoup(html2, 'lxml')
    
    table1 = soup1.find('table')
    table2 = soup2.find('table')
    
    # 获取第一个表格的最后一行
    rows1 = table1.find_all('tr')
    last_row1 = rows1[-1]
    
    # 获取第二个表格的第一行
    rows2 = table2.find_all('tr')
    first_row2 = rows2[0]
    
    # 合并最后一行和第一行的内容
    cells1 = last_row1.find_all(['td', 'th'])
    cells2 = first_row2.find_all(['td', 'th'])
    
    min_cells = min(len(cells1), len(cells2))
    for i in range(min_cells):
        text1 = cells1[i].get_text(strip=True)
        text2 = cells2[i].get_text(strip=True)
        if text1 and text2:
            cells1[i].string = text1 + text2
    
    # 添加剩余的行
    tbody1 = table1.find('tbody')
    if not tbody1:
        tbody1 = soup1.new_tag('tbody')
        table1.append(tbody1)
    
    for row in rows2[1:]:  # 跳过第一行
        new_row = soup1.new_tag('tr')
        for cell in row.find_all(['td', 'th']):
            new_cell = soup1.new_tag('td')
            new_cell.string = cell.get_text(strip=True)
            new_row.append(new_cell)
        tbody1.append(new_row)
    
    return str(soup1)


def main():
    """主测试函数"""
    print("手写HTML表格合并测试")
    print("="*50)
    
    test_basic_merge()
    test_merge_first_row()
    test_complex_table()
    
    print("\n" + "="*50)
    print("测试完成！")


if __name__ == "__main__":
    main()
